import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  AppTheme._();

  static ThemeData getTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: primaryBlueAccent,
        secondary: secondaryGreen,
        surface: primarySoft,
        onPrimary: textColorWhite,
        onSecondary: textColorWhite,
        onSurface: textColorWhite,
        error: secondaryRed,
        onError: textColorWhite,
      ),
      scaffoldBackgroundColor: primaryDark,
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryDark,
        foregroundColor: textColorWhite,
        elevation: 0,
      ),
    );
  }

  // Colors Primary - Modern Cinema Theme
  static const Color primaryDark = Color(0xff0D1117); // Deep space black
  static const Color primarySoft = Color(0xff161B22); // Soft dark gray
  static const Color primaryBlueAccent = Color(0xff6366F1); // Modern indigo

  // Colors Secondary - Vibrant Cinema Accents
  static const Color secondaryGreen = Color(0xff10B981); // Emerald green
  static const Color secondaryOrange = Color(0xffF59E0B); // Amber gold
  static const Color secondaryRed = Color(0xffEF4444); // Modern red

  // Text Color - Enhanced Readability
  static const Color textColorBlack = Color(0xff0F172A); // Slate black
  static const Color textColorDarkGrey = Color(0xff64748B); // Slate gray
  static const Color textColorGrey = Color(0xff94A3B8); // Light slate gray
  static const Color textColorWhiteGrey = Color(0xffF1F5F9); // Slate white
  static const Color textColorWhite = Color(0xffFFFFFF); // Pure white
  static const Color textColorLineDark =
      Color(0xffE2E8F0); // Light slate border

  // Heading text style SemiBold
  static TextStyle h1Semibold = GoogleFonts.montserrat(
    fontSize: 28,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle h2Semibold = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle h3Semibold = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle h4Semibold = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle h5Semibold = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle h6Semibold = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle h7Semibold = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );

  // Heading text style Medium
  static TextStyle h1Medium = GoogleFonts.montserrat(
    fontSize: 28,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle h2Medium = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle h3Medium = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle h4Medium = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle h5Medium = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle h6Medium = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle h7Medium = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );

  // Heading text style Regular
  static TextStyle h1Regular = GoogleFonts.montserrat(
    fontSize: 28,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
  static TextStyle h2Regular = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
  static TextStyle h3Regular = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
  static TextStyle h4Regular = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
  static TextStyle h5Regular = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
  static TextStyle h6Regular = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
  static TextStyle h7Regular = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );

  // Paragraph TextStyle
  static TextStyle bodySemibold = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: textColorWhite,
  );
  static TextStyle bodyMedium = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: textColorWhite,
  );
  static TextStyle bodyRegular = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textColorWhite,
  );
}
